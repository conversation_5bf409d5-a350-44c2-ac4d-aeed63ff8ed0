<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-6xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">{{ isEditMode ? 'Edit Service Order' : 'Create New Service Order' }}</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="serialNumber" class="block text-sm font-medium text-gray-700 mb-1">Serial Number *</label>
                <input
                  id="serialNumber"
                  v-model="form.serialNumber"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter serial number"
                />
              </div>
              <div>
                <label for="serviceOrderNo" class="block text-sm font-medium text-gray-700 mb-1">Service Order No *</label>
                <input
                  id="serviceOrderNo"
                  v-model="form.serviceOrderNo"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter service order number"
                />
              </div>
              <div>
                <label for="customer" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
                <SearchableDropdown
                  v-model="form.customer"
                  :options="customerOptions"
                  placeholder="Select customer"
                  required
                />
              </div>
              <div>
                <label for="invoice" class="block text-sm font-medium text-gray-700 mb-1">Invoice</label>
                <input
                  id="invoice"
                  v-model="form.invoice"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter invoice number"
                />
              </div>
            </div>
          </div>

          <!-- Work Details -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Work Details</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="natureOfWork" class="block text-sm font-medium text-gray-700 mb-1">Nature of Work *</label>
                <input
                  id="natureOfWork"
                  v-model="form.workDetails.natureOfWork"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter nature of work"
                />
              </div>
              <div>
                <label for="installer" class="block text-sm font-medium text-gray-700 mb-1">Installer *</label>
                <input
                  id="installer"
                  v-model="form.workDetails.installer"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter installer name"
                />
              </div>
              <div>
                <label for="startTime" class="block text-sm font-medium text-gray-700 mb-1">Start Time *</label>
                <input
                  id="startTime"
                  v-model="form.workDetails.startTime"
                  type="datetime-local"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <div>
                <label for="endTime" class="block text-sm font-medium text-gray-700 mb-1">End Time</label>
                <input
                  id="endTime"
                  v-model="form.workDetails.endTime"
                  type="datetime-local"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div class="mt-4">
              <label for="tests" class="block text-sm font-medium text-gray-700 mb-1">Tests Performed</label>
              <div class="flex flex-wrap gap-2 mb-2">
                <span
                  v-for="(test, index) in form.workDetails.tests"
                  :key="index"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ test }}
                  <button
                    type="button"
                    @click="removeTest(index)"
                    class="ml-1 text-blue-600 hover:text-blue-800"
                  >
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              </div>
              <div class="flex gap-2">
                <input
                  v-model="newTest"
                  type="text"
                  placeholder="Enter test name"
                  @keyup.enter="addTest"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  type="button"
                  @click="addTest"
                  class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  Add
                </button>
              </div>
            </div>
          </div>

          <!-- Test Results -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Test Results</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="lanSpeedTest" class="block text-sm font-medium text-gray-700 mb-1">LAN Speed Test</label>
                <input
                  id="lanSpeedTest"
                  v-model="form.testResults.lanSpeedTest"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., 100 Mbps"
                />
              </div>
              <div>
                <label for="wifi24GHzSpeedTest" class="block text-sm font-medium text-gray-700 mb-1">WiFi 2.4GHz Speed Test</label>
                <input
                  id="wifi24GHzSpeedTest"
                  v-model="form.testResults.wifi24GHzSpeedTest"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., 50 Mbps"
                />
              </div>
              <div>
                <label for="wifi5GHzSpeedTest" class="block text-sm font-medium text-gray-700 mb-1">WiFi 5GHz Speed Test</label>
                <input
                  id="wifi5GHzSpeedTest"
                  v-model="form.testResults.wifi5GHzSpeedTest"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., 80 Mbps"
                />
              </div>
              <div>
                <label for="wifiCoverageSpeedTest" class="block text-sm font-medium text-gray-700 mb-1">WiFi Coverage Speed Test</label>
                <input
                  id="wifiCoverageSpeedTest"
                  v-model="form.testResults.wifiCoverageSpeedTest"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="e.g., Good"
                />
              </div>
            </div>
          </div>

          <!-- Cabling Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Cabling Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="outdoorLength" class="block text-sm font-medium text-gray-700 mb-1">Outdoor Length (m)</label>
                <input
                  id="outdoorLength"
                  v-model.number="form.cabling.outdoorLength"
                  type="number"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.0"
                />
              </div>
              <div>
                <label for="indoorLength" class="block text-sm font-medium text-gray-700 mb-1">Indoor Length (m)</label>
                <input
                  id="indoorLength"
                  v-model.number="form.cabling.indoorLength"
                  type="number"
                  step="0.1"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="0.0"
                />
              </div>
              <div>
                <label for="splitterDetails" class="block text-sm font-medium text-gray-700 mb-1">Splitter Details</label>
                <input
                  id="splitterDetails"
                  v-model="form.cabling.splitterDetails"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter splitter details"
                />
              </div>
              <div>
                <label for="taggingDetails" class="block text-sm font-medium text-gray-700 mb-1">Tagging Details</label>
                <input
                  id="taggingDetails"
                  v-model="form.cabling.taggingDetails"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter tagging details"
                />
              </div>
            </div>
          </div>

          <!-- Declaration -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Customer Declaration</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="confirmedBy" class="block text-sm font-medium text-gray-700 mb-1">Confirmed By</label>
                <input
                  id="confirmedBy"
                  v-model="form.declaration.confirmedBy"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter customer name"
                />
              </div>
              <div>
                <label for="myKadNo" class="block text-sm font-medium text-gray-700 mb-1">MyKad No</label>
                <input
                  id="myKadNo"
                  v-model="form.declaration.myKadNo"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter MyKad number"
                />
              </div>
              <div>
                <label for="declarationDate" class="block text-sm font-medium text-gray-700 mb-1">Date</label>
                <input
                  id="declarationDate"
                  v-model="form.declaration.date"
                  type="datetime-local"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          <!-- Remarks -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Remarks</h4>
            <textarea
              v-model="form.remarks"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter any additional remarks or notes"
            ></textarea>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isEditMode ? 'Updating...' : 'Creating...' }}
              </span>
              <span v-else>
                {{ isEditMode ? 'Update Service Order' : 'Create Service Order' }}
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ServiceOrderService, CustomerService } from '@/services/api'
import type { ServiceOrder, CreateServiceOrderRequest, Customer } from '@/types'
import SearchableDropdown from '@/components/SearchableDropdown.vue'

// Props
interface Props {
  serviceOrder?: ServiceOrder | null
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  serviceOrder: null,
  isEditMode: false
})

// Emits
const emit = defineEmits<{
  close: []
  saved: []
}>()

// Reactive data
const loading = ref(false)
const customers = ref<Customer[]>([])
const newTest = ref('')

// Form data
const form = ref<CreateServiceOrderRequest>({
  serialNumber: '',
  serviceOrderNo: '',
  customer: '',
  invoice: '',
  workDetails: {
    natureOfWork: '',
    installer: '',
    tests: [],
    startTime: '',
    endTime: ''
  },
  testResults: {
    lanSpeedTest: '',
    wifi24GHzSpeedTest: '',
    wifi5GHzSpeedTest: '',
    wifiCoverageSpeedTest: '',
    deviceAps: [],
    dectPhones: [],
    connectors: [],
    otherDevices: []
  },
  devices: [],
  cabling: {
    outdoorLength: 0,
    indoorLength: 0,
    splitterDetails: '',
    taggingDetails: ''
  },
  installation: {
    standardItems: [],
    additionalItems: [],
    nonStandard: [],
    totalAmount: 0
  },
  declaration: {
    confirmedBy: '',
    myKadNo: '',
    date: ''
  },
  remarks: ''
})

// Customer options for dropdown
const customerOptions = computed(() => {
  return customers.value.map(customer => ({
    value: customer.id,
    label: customer.name,
    subtitle: customer.contactPerson
  }))
})

// Load customers
async function loadCustomers() {
  try {
    const response = await CustomerService.getCustomers({ limit: 1000 })
    if (response.success && response.data?.data) {
      customers.value = response.data.data.data || []
    }
  } catch (error) {
    console.error('Error loading customers:', error)
  }
}

// Initialize form
function initializeForm() {
  if (props.isEditMode && props.serviceOrder) {
    const so = props.serviceOrder
    form.value = {
      serialNumber: so.serialNumber || '',
      serviceOrderNo: so.serviceOrderNo || '',
      customer: so.customer || '',
      invoice: so.invoice || '',
      workDetails: {
        natureOfWork: so.workDetails?.natureOfWork || '',
        installer: so.workDetails?.installer || '',
        tests: so.workDetails?.tests || [],
        startTime: so.workDetails?.startTime ? formatDateTimeLocal(so.workDetails.startTime) : '',
        endTime: so.workDetails?.endTime ? formatDateTimeLocal(so.workDetails.endTime) : ''
      },
      testResults: {
        lanSpeedTest: so.testResults?.lanSpeedTest || '',
        wifi24GHzSpeedTest: so.testResults?.wifi24GHzSpeedTest || '',
        wifi5GHzSpeedTest: so.testResults?.wifi5GHzSpeedTest || '',
        wifiCoverageSpeedTest: so.testResults?.wifiCoverageSpeedTest || '',
        deviceAps: so.testResults?.deviceAps || [],
        dectPhones: so.testResults?.dectPhones || [],
        connectors: so.testResults?.connectors || [],
        otherDevices: so.testResults?.otherDevices || []
      },
      devices: so.devices || [],
      cabling: {
        outdoorLength: so.cabling?.outdoorLength || 0,
        indoorLength: so.cabling?.indoorLength || 0,
        splitterDetails: so.cabling?.splitterDetails || '',
        taggingDetails: so.cabling?.taggingDetails || ''
      },
      installation: {
        standardItems: so.installation?.standardItems || [],
        additionalItems: so.installation?.additionalItems || [],
        nonStandard: so.installation?.nonStandard || [],
        totalAmount: so.installation?.totalAmount || 0
      },
      declaration: {
        confirmedBy: so.declaration?.confirmedBy || '',
        myKadNo: so.declaration?.myKadNo || '',
        date: so.declaration?.date ? formatDateTimeLocal(so.declaration.date) : ''
      },
      remarks: so.remarks || ''
    }
  } else {
    // Set default values for new service order
    const now = new Date()
    form.value.workDetails.startTime = formatDateTimeLocal(now.toISOString())
    form.value.declaration.date = formatDateTimeLocal(now.toISOString())
  }
}

// Format date for datetime-local input
function formatDateTimeLocal(dateString: string): string {
  const date = new Date(dateString)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day}T${hours}:${minutes}`
}

// Add test
function addTest() {
  if (newTest.value.trim()) {
    form.value.workDetails.tests.push(newTest.value.trim())
    newTest.value = ''
  }
}

// Remove test
function removeTest(index: number) {
  form.value.workDetails.tests.splice(index, 1)
}

// Handle form submission
async function handleSubmit() {
  loading.value = true
  try {
    // Convert datetime-local values to ISO strings
    const submitData = {
      ...form.value,
      workDetails: {
        ...form.value.workDetails,
        startTime: form.value.workDetails.startTime ? new Date(form.value.workDetails.startTime).toISOString() : '',
        endTime: form.value.workDetails.endTime ? new Date(form.value.workDetails.endTime).toISOString() : ''
      },
      declaration: {
        ...form.value.declaration,
        date: form.value.declaration.date ? new Date(form.value.declaration.date).toISOString() : ''
      }
    }

    let response
    if (props.isEditMode && props.serviceOrder) {
      response = await ServiceOrderService.updateServiceOrder(props.serviceOrder.id, submitData)
    } else {
      response = await ServiceOrderService.createServiceOrder(submitData)
    }

    if (response.success) {
      emit('saved')
    } else {
      alert('Failed to save service order: ' + (response.error || 'Unknown error'))
    }
  } catch (error) {
    console.error('Error saving service order:', error)
    alert('Error saving service order')
  } finally {
    loading.value = false
  }
}

// Watch for prop changes
watch(() => props.serviceOrder, initializeForm, { immediate: true })

// Initialize
onMounted(() => {
  loadCustomers()
  initializeForm()
})
</script>
